{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 3005", "debug": "NODE_OPTIONS='--inspect' next dev --port 3005", "build": "next build", "start": "next start", "lint": "next lint", "studio": "npx prisma studio", "prisma:generate": "npx prisma generate"}, "dependencies": {"@auth/prisma-adapter": "^2.8.0", "@azure/identity": "^4.10.2", "@azure/storage-blob": "^12.27.0", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@next-auth/prisma-adapter": "^1.0.7", "@octokit/rest": "^21.1.1", "@prisma/client": "5.13.0", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-context-menu": "^2.2.7", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-hover-card": "^1.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-menubar": "^1.1.7", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-toggle-group": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.0", "@react-pdf/renderer": "^4.1.3", "@sentry/nextjs": "^9.31.0", "@types/bcrypt": "^5.0.2", "@types/bcryptjs": "^3.0.0", "@types/react-google-recaptcha": "^2.1.9", "@types/uuid": "^10.0.0", "bcryptjs": "^3.0.2", "bufferutil": "^4.0.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^2.30.0", "dotenv": "^16.5.0", "framer-motion": "^12.7.3", "html2canvas": "^1.4.1", "input-otp": "^1.4.2", "lucide-react": "^0.487.0", "next": "^14.1.0", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "nextra": "^2.13.3", "nextra-theme-docs": "^2.13.3", "node-fetch": "^3.3.2", "prisma": "^5.22.0", "qrcode.react": "^4.2.0", "react": "^18.2.0", "react-day-picker": "8.10.1", "react-dom": "^18.3.1", "react-google-recaptcha": "^3.1.0", "react-hook-form": "^7.55.0", "recharts": "^2.15.2", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.5", "twilio": "^5.6.0", "utf-8-validate": "^6.0.5", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.16", "eslint": "^8.0.0", "eslint-config-next": "13.5.6", "postcss": "^8.4.31", "prisma": "5.13.0", "tailwindcss": "^3", "tailwindcss-animate": "^1.0.7", "ts-node": "^10.9.2", "typescript": "^5"}}