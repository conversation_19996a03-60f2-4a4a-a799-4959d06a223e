import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    console.log("🔓 FIDELIUS DECRYPT API - Starting");

    const body = await request.json();

    // Log exactly what we received
    console.log("📥 RECEIVED PAYLOAD:", {
      hasEncryptedData: !!body.encrypted_data,
      encryptedDataLength: body.encrypted_data?.length || 0,
      hasRequesterNonce: !!body.requester_nonce,
      requesterNonceValue: body.requester_nonce?.substring(0, 20) + "..." || "MISSING",
      hasSenderNonce: !!body.sender_nonce,
      senderNonceValue: body.sender_nonce?.substring(0, 20) + "..." || "MISSING",
      hasRequesterPrivateKey: !!body.requester_private_key,
      requesterPrivateKeyValue: body.requester_private_key?.substring(0, 20) + "..." || "MISSING",
      hasSenderPublicKey: !!body.sender_public_key,
      senderPublicKeyValue: body.sender_public_key?.substring(0, 50) + "..." || "MISSING",
    });

    const requiredFields = ['encrypted_data', 'requester_nonce', 'sender_nonce', 'requester_private_key', 'sender_public_key'];
    const missingFields = requiredFields.filter(field => !body[field]);
    if (missingFields.length > 0) {
      console.error("❌ Missing required fields:", missingFields);
      return NextResponse.json({ success: false, error: `Missing required fields: ${missingFields.join(', ')}` }, { status: 400 });
    }

    const fideliusPayload = {
      encrypted_data: body.encrypted_data,
      requester_nonce: body.requester_nonce,
      sender_nonce: body.sender_nonce,
      requester_private_key: body.requester_private_key,
      sender_public_key: body.sender_public_key,
    };

    // Log exactly what we're sending to Fidelius API
    console.log("📤 SENDING TO FIDELIUS API:", {
      url: "https://api.arancare.flinkk.io/fidelius-api/decrypt",
      method: "POST",
      headers: { "Content-Type": "application/json" },
      payloadKeys: Object.keys(fideliusPayload),
      encryptedDataLength: fideliusPayload.encrypted_data.length,
      requesterNonce: fideliusPayload.requester_nonce,
      senderNonce: fideliusPayload.sender_nonce,
      requesterPrivateKey: fideliusPayload.requester_private_key,
      senderPublicKey: fideliusPayload.sender_public_key?.substring(0, 100) + "...",
    });

    console.log("🔓 Calling external Fidelius API");
    const startTime = Date.now();
    const fideliusResponse = await fetch("https://api.arancare.flinkk.io/fidelius-api/decrypt", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(fideliusPayload),
    });

    const responseTime = Date.now() - startTime;
    console.log(`⏱️ Fidelius API response time: ${responseTime}ms`);
    console.log(`📊 Fidelius API status: ${fideliusResponse.status} ${fideliusResponse.statusText}`);

    if (!fideliusResponse.ok) {
      let errorDetails = "";
      try {
        errorDetails = await fideliusResponse.text();
        console.error("❌ Fidelius API HTTP Error Response:", errorDetails);
      } catch (e) {
        console.error("❌ Could not read Fidelius API error response");
      }
      return NextResponse.json({ success: false, error: `Fidelius API returned ${fideliusResponse.status}`, details: errorDetails }, { status: fideliusResponse.status });
    }

    const responseText = await fideliusResponse.text();
    console.log("📥 RAW FIDELIUS API RESPONSE:", {
      hasContent: !!responseText,
      length: responseText?.length || 0,
      preview: responseText?.substring(0, 500) + (responseText?.length > 500 ? "..." : ""),
    });

    let fideliusResult;
    try {
      fideliusResult = JSON.parse(responseText);
      console.log("✅ Fidelius API JSON parsed successfully");
    } catch (parseError) {
      console.error("❌ JSON Parse Error:", parseError);
      console.error("❌ Raw response that failed to parse:", responseText);
      return NextResponse.json({ success: false, error: `Failed to parse Fidelius API response: ${parseError instanceof Error ? parseError.message : String(parseError)}` }, { status: 500 });
    }

    console.log("🔍 FIDELIUS RESULT STRUCTURE:", {
      hasSuccess: 'success' in fideliusResult,
      successValue: fideliusResult?.success,
      hasData: 'data' in fideliusResult,
      dataKeys: fideliusResult?.data ? Object.keys(fideliusResult.data) : [],
      hasDecryptedData: fideliusResult?.data?.decryptedData ? true : false,
      decryptedDataLength: fideliusResult?.data?.decryptedData?.length || 0,
      fullStructure: Object.keys(fideliusResult),
    });

    // Handle the exact format from ex.json
    if (fideliusResult?.success && fideliusResult?.data?.decryptedData) {
      try {
        const fhirBundle = JSON.parse(fideliusResult.data.decryptedData);
        console.log("✅ Decryption completed successfully:", {
          bundleType: fhirBundle.resourceType,
          entriesCount: fhirBundle.entry?.length || 0,
          bundleId: fhirBundle.id,
        });

        return NextResponse.json({
          success: true,
          fhirBundle: fhirBundle
        });
      } catch (bundleParseError) {
        console.error("❌ Failed to parse FHIR bundle from decrypted data:", bundleParseError);
        console.error("❌ Decrypted data that failed to parse:", fideliusResult.data.decryptedData?.substring(0, 1000));
        return NextResponse.json({ success: false, error: `Failed to parse FHIR bundle: ${bundleParseError instanceof Error ? bundleParseError.message : String(bundleParseError)}` }, { status: 500 });
      }
    } else {
      console.error("❌ Invalid Fidelius response format:", {
        hasSuccess: 'success' in fideliusResult,
        successValue: fideliusResult?.success,
        hasData: 'data' in fideliusResult,
        hasDecryptedData: fideliusResult?.data?.decryptedData ? true : false,
        fullResponse: fideliusResult,
      });
      return NextResponse.json({ success: false, error: "Invalid Fidelius response format - missing success or decryptedData" }, { status: 500 });
    }

  } catch (error) {
    console.error("❌ Fidelius decrypt error:", error);
    return NextResponse.json({ success: false, error: error instanceof Error ? error.message : String(error) }, { status: 500 });
  }
}
