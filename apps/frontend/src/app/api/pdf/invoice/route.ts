import React from "react";
import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/session";
import { prisma } from "@/lib/prisma";
import { generatePdfBuffer } from "@/services/pdf-generation/pdf-generator";
import { validatePdfBuffer } from "@/lib/fhir/binary-utils";

export async function GET(request: NextRequest) {
  try {
    console.log("=== INVOICE PDF ENDPOINT CALLED ===");
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const consultationId = searchParams.get("consultationId");
    const invoiceId = searchParams.get("invoiceId");

    // Fetch invoice data following immunization pattern
    let invoices;
    if (invoiceId) {
      // Get specific invoice record
      const singleInvoice = await prisma.invoice.findUnique({
        where: {
          id: invoiceId,
        },
        include: {
          items: {
            orderBy: { sequence: "asc" },
          },
          consultation: {
            include: {
              patient: {
                include: {
                  abhaProfile: true,
                },
              },
              doctor: {
                include: {
                  user: true,
                },
              },
              branch: {
                include: {
                  organization: true,
                },
              },
            },
          },
        },
      });

      if (!singleInvoice || singleInvoice.organizationId !== user.organizationId) {
        return NextResponse.json(
          { error: "Invoice not found or access denied" },
          { status: 404 },
        );
      }

      invoices = [singleInvoice];
    } else if (consultationId) {
      // Get invoices for specific consultation within the user's organization
      console.log('Searching for invoices with:', {
        consultationId,
        organizationId: user.organizationId
      });

      invoices = await prisma.invoice.findMany({
        where: {
          consultationId,
          organizationId: user.organizationId,
        },
        include: {
          items: {
            orderBy: { sequence: "asc" },
          },
          consultation: {
            include: {
              patient: {
                include: {
                  abhaProfile: true,
                },
              },
              doctor: {
                include: {
                  user: true,
                },
              },
              branch: {
                include: {
                  organization: true,
                },
              },
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      console.log('Found invoices:', invoices.length);
      if (invoices.length > 0) {
        console.log('First invoice:', {
          id: invoices[0].id,
          invoiceNumber: invoices[0].invoiceNumber,
          itemsCount: invoices[0].items.length,
          totalAmount: invoices[0].totalAmount
        });
      }
    } else {
      return NextResponse.json(
        { error: "Either consultationId or invoiceId is required" },
        { status: 400 },
      );
    }

    if (!invoices || invoices.length === 0) {
      return NextResponse.json(
        { error: "No invoice found" },
        { status: 404 },
      );
    }

    // Get organization info for the user's organization
    const organization = await prisma.organization.findUnique({
      where: {
        id: user.organizationId,
      },
    });

    // Prepare data for PDF generation following immunization pattern
    const firstInvoice = invoices[0];
    const consultation = firstInvoice.consultation;
    const patient = consultation?.patient;
    const doctor = consultation?.doctor;

    // Prepare invoice data for PDF generation following immunization pattern
    const invoiceData = {
      id: firstInvoice.id,
      invoiceNumber: firstInvoice.invoiceNumber,
      invoiceDate: firstInvoice.invoiceDate,
      dueDate: firstInvoice.dueDate,
      status: firstInvoice.status,
      type: firstInvoice.type,
      subtotal: firstInvoice.subtotal?.toString() || "0",
      taxAmount: firstInvoice.taxAmount?.toString() || "0",
      discountAmount: firstInvoice.discountAmount?.toString() || "0",
      totalAmount: firstInvoice.totalAmount?.toString() || "0",
      currency: firstInvoice.currency || "INR",
      paymentTerms: firstInvoice.paymentTerms || "Net 30",
      paymentStatus: firstInvoice.paymentStatus || "pending",
      notes: firstInvoice.notes || "",
      consultation: {
        id: consultation?.id || "N/A",
        consultationDate: consultation?.consultationDate || new Date(),
      },
      patient: patient || {},
      doctor: doctor || {},
      organization: consultation?.branch?.organization || organization,
      branch: consultation?.branch || {},
      items: firstInvoice.items?.map((item: any) => ({
        ...item,
        quantity: item.quantity?.toString() || "0",
        unitPrice: item.unitPrice?.toString() || "0",
        mrp: item.mrp?.toString() || "0",
        discountAmount: item.discountAmount?.toString() || "0",
        taxRate: item.taxRate?.toString() || "0",
        cgstAmount: item.cgstAmount?.toString() || "0",
        sgstAmount: item.sgstAmount?.toString() || "0",
        igstAmount: item.igstAmount?.toString() || "0",
        totalAmount: item.totalAmount?.toString() || "0",
      })) || [],
    };

    console.log("Invoice data for PDF:", JSON.stringify(invoiceData, null, 2));
    // Generate PDF using the same approach as immunization PDF
    const { InvoicePDF } = await import(
      "@/services/pdf-templates/InvoicePDF"
    );
    const pdfBuffer = await generatePdfBuffer(
      React.createElement(InvoicePDF, {
        invoiceData: invoiceData as any,
      }),
    );

    // Validate generated PDF
    if (!validatePdfBuffer(pdfBuffer)) {
      return NextResponse.json(
        { error: "Failed to generate valid PDF" },
        { status: 500 },
      );
    }

    // Generate filename following immunization pattern
    const date = new Date().toISOString().split("T")[0];
    let filename;

    if (invoiceId && invoices.length > 0) {
      // Individual invoice filename
      const invoice = invoices[0];
      const patientName = invoice.consultation?.patient
        ? `${invoice.consultation.patient.firstName}_${invoice.consultation.patient.lastName}`
        : "Unknown_Patient";
      const invoiceDate = new Date(invoice.invoiceDate || new Date())
        .toISOString()
        .split("T")[0];
      filename = `Invoice_${invoice.invoiceNumber}_${patientName}_${invoiceDate}.pdf`;
    } else if (consultationId && invoices.length > 0) {
      // Consultation-specific invoice filename
      const invoice = invoices[0];
      const patientName = invoice.consultation?.patient
        ? `${invoice.consultation.patient.firstName}_${invoice.consultation.patient.lastName}`
        : "Unknown_Patient";
      filename = `Invoice_${invoice.invoiceNumber}_${patientName}_${date}.pdf`;
    } else {
      // Fallback filename
      filename = `Invoice_${date}.pdf`;
    }

    // Return PDF for download
    return new NextResponse(pdfBuffer, {
      headers: {
        "Content-Type": "application/pdf",
        "Content-Disposition": `attachment; filename="${filename}"`,
        "Content-Length": pdfBuffer.length.toString(),
        "Cache-Control": "no-cache, no-store, must-revalidate",
        Pragma: "no-cache",
        Expires: "0",
      },
    });
  } catch (error) {
    console.error("Error generating invoice PDF:", error);
    return NextResponse.json(
      { error: "Failed to generate PDF" },
      { status: 500 },
    );
  }
}
